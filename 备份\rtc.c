/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    rtc.c
  * @brief   This file provides code for the configuration
  *          of the RTC instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "rtc.h"

/* USER CODE BEGIN 0 */
/* USER CODE END 0 */

RTC_HandleTypeDef hrtc;

/* RTC init function */
void MX_RTC_Init(void)
{

  /* USER CODE BEGIN RTC_Init 0 */
  /* USER CODE END RTC_Init 0 */

  RTC_TimeTypeDef sTime = {0};
  RTC_DateTypeDef sDate = {0};

  /* USER CODE BEGIN RTC_Init 1 */
  /* USER CODE END RTC_Init 1 */

  /** Initialize RTC Only
  */
  hrtc.Instance = RTC;
  hrtc.Init.HourFormat = RTC_HOURFORMAT_24;
  hrtc.Init.AsynchPrediv = 127;
  hrtc.Init.SynchPrediv = 255;
  hrtc.Init.OutPut = RTC_OUTPUT_DISABLE;
  hrtc.Init.OutPutRemap = RTC_OUTPUT_REMAP_NONE;
  hrtc.Init.OutPutPolarity = RTC_OUTPUT_POLARITY_HIGH;
  hrtc.Init.OutPutType = RTC_OUTPUT_TYPE_OPENDRAIN;
  if (HAL_RTC_Init(&hrtc) != HAL_OK)
  {
    Error_Handler();
  }

  /* USER CODE BEGIN Check_RTC_BKUP */
  if (HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR0) == 0x32F2) {
    return;
  }

  /* USER CODE END Check_RTC_BKUP */

  /** Initialize RTC and set the Time and Date
  */
  sTime.Hours = 0x0;
  sTime.Minutes = 0x0;
  sTime.Seconds = 0x0;
  sTime.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
  sTime.StoreOperation = RTC_STOREOPERATION_RESET;
  if (HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BCD) != HAL_OK)
  {
    Error_Handler();
  }
  sDate.WeekDay = RTC_WEEKDAY_MONDAY;
  sDate.Month = RTC_MONTH_JANUARY;
  sDate.Date = 0x1;
  sDate.Year = 0x25;

  if (HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BCD) != HAL_OK)
  {
    Error_Handler();
  }

  /** Enable the WakeUp
  */
  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, 10, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN RTC_Init 2 */
  HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR0, 0x32F2);
  /* USER CODE END RTC_Init 2 */

}

void HAL_RTC_MspInit(RTC_HandleTypeDef* rtcHandle)
{

  if(rtcHandle->Instance==RTC)
  {
  /* USER CODE BEGIN RTC_MspInit 0 */
  HAL_PWR_EnableBkUpAccess();
  /* USER CODE END RTC_MspInit 0 */
    /* RTC clock enable */
    __HAL_RCC_RTC_ENABLE();

    /* RTC interrupt Init */
    HAL_NVIC_SetPriority(RTC_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(RTC_IRQn);
  /* USER CODE BEGIN RTC_MspInit 1 */
  /* USER CODE END RTC_MspInit 1 */
  }
}

void HAL_RTC_MspDeInit(RTC_HandleTypeDef* rtcHandle)
{

  if(rtcHandle->Instance==RTC)
  {
  /* USER CODE BEGIN RTC_MspDeInit 0 */
  /* USER CODE END RTC_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_RTC_DISABLE();

    /* RTC interrupt Deinit */
    HAL_NVIC_DisableIRQ(RTC_IRQn);
  /* USER CODE BEGIN RTC_MspDeInit 1 */
  /* USER CODE END RTC_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
void RTC_GetDateTime(RTC_TimeTypeDef *time, RTC_DateTypeDef *date) {
  HAL_RTC_GetTime(&hrtc, time, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, date, RTC_FORMAT_BIN);
}

HAL_StatusTypeDef RTC_SetDateTime(uint8_t hour, uint8_t minute, uint8_t second,
                               uint8_t day, uint8_t month, uint8_t year) {
  HAL_StatusTypeDef timeStatus, dateStatus;
  timeStatus = RTC_SetTime(hour, minute, second);
  dateStatus = RTC_SetDate(day, month, year);
  if (timeStatus == HAL_OK && dateStatus == HAL_OK) {
    HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR0, 0x32F2);
    return HAL_OK;
  }

  return HAL_ERROR;
}

HAL_StatusTypeDef RTC_SetTime(uint8_t hour, uint8_t minute, uint8_t second) {
  RTC_TimeTypeDef sTime = {0}
;
  if (hour >= 24 || minute >= 60 || second >= 60) {
    return HAL_ERROR;
  }

  sTime.Hours = hour;
  sTime.Minutes = minute;
  sTime.Seconds = second;
  sTime.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
  sTime.StoreOperation = RTC_STOREOPERATION_SET;
  return HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
}

HAL_StatusTypeDef RTC_SetDate(uint8_t day, uint8_t month, uint8_t year) {
  RTC_DateTypeDef sDate = {0}
;
  if (day < 1 || day > 31 || month < 1 || month > 12) {
    return HAL_ERROR;
  }

  sDate.Date = day;
  sDate.Month = month;
  sDate.Year = year;
  sDate.WeekDay = RTC_WEEKDAY_MONDAY;
  return HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
}

int RTC_IsWorkTime(RTC_TimeTypeDef *time, uint8_t startHour, uint8_t startMinute,
                  uint8_t endHour, uint8_t endMinute) {
  uint16_t currentMinutes = time->Hours * 60 + time->Minutes;
  uint16_t startMinutes = startHour * 60 + startMinute;
  uint16_t endMinutes = endHour * 60 + endMinute;
  if (startMinutes <= endMinutes) {
    return (currentMinutes >= startMinutes && currentMinutes <= endMinutes);
  }
 else {
    return (currentMinutes >= startMinutes || currentMinutes <= endMinutes);
  }

}

void RTC_ChangeWakeUpTime(uint32_t seconds) {
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);
  uint32_t wakeUpCounter = seconds - 1;
  uint32_t wakeUpClock = RTC_WAKEUPCLOCK_CK_SPRE_16BITS;
  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, wakeUpCounter, wakeUpClock) != HAL_OK) {
    Error_Handler();
  }

  HAL_NVIC_EnableIRQ(RTC_IRQn);
}

void RTC_SaveSettings(void) {
  HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR0, 0x32F2);
}

void RTC_LoadSettings(void) {
  if (HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR0) == 0x32F2) {
  }

}

/* USER CODE END 1 */
