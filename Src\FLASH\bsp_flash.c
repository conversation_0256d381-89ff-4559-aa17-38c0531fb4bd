/**
  ******************************************************************************
  * @file    bsp_flash.c
  * @brief   FLASH read/write operations wrapper
  ******************************************************************************
  */

#include "FLASH/bsp_flash.h"
#include <string.h>

static uint8_t flashDataBuffer[FLASH_PAGE_SIZE] = {0};
static uint8_t isFlashDataLoaded = 0;

static HAL_StatusTypeDef Flash_LoadData(void);
static HAL_StatusTypeDef Flash_SaveData(void);

// Initialize FLASH
HAL_StatusTypeDef FLASH_Init(void)
{
  return Flash_LoadData();
}

// Load data from FLASH to buffer
static HAL_StatusTypeDef Flash_LoadData(void)
{
  HAL_StatusTypeDef status;

  status = FLASH_ReadData(flashDataBuffer, FLASH_PAGE_SIZE);
  if (status == HAL_OK)
  {
    isFlashDataLoaded = 1;
  }

  return status;
}

// Save buffer data to FLASH
static HAL_StatusTypeDef Flash_SaveData(void)
{
  return FLASH_WriteData(flashDataBuffer, FLASH_PAGE_SIZE);
}

// Erase FLASH user area
HAL_StatusTypeDef FLASH_EraseUserArea(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  FLASH_EraseInitTypeDef eraseInit;
  uint32_t pageError = 0;
  uint32_t primask;

  primask = __get_PRIMASK();
  __disable_irq();

  status = HAL_FLASH_Unlock();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_WRPERR | FLASH_FLAG_PGAERR |
                         FLASH_FLAG_SIZERR | FLASH_FLAG_OPTVERR | FLASH_FLAG_RDERR |
                         FLASH_FLAG_FWWERR | FLASH_FLAG_NOTZEROERR);

  eraseInit.TypeErase = FLASH_TYPEERASE_PAGES;
  eraseInit.PageAddress = FLASH_USER_START_ADDR;
  eraseInit.NbPages = 1;

  status = HAL_FLASHEx_Erase(&eraseInit, &pageError);

  HAL_FLASH_Lock();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Write data to FLASH
HAL_StatusTypeDef FLASH_WriteData(uint8_t *pData, uint16_t DataSize)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t address = FLASH_USER_START_ADDR;
  uint32_t data = 0;
  uint32_t i = 0;
  uint32_t primask;

  if (pData == NULL || DataSize > FLASH_PAGE_SIZE)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  status = FLASH_EraseUserArea();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  status = HAL_FLASH_Unlock();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_WRPERR | FLASH_FLAG_PGAERR |
                         FLASH_FLAG_SIZERR | FLASH_FLAG_OPTVERR | FLASH_FLAG_RDERR |
                         FLASH_FLAG_FWWERR | FLASH_FLAG_NOTZEROERR);

  for (i = 0; i < DataSize; i += 4)
  {
    data = 0;
    if (i < DataSize) data |= ((uint32_t)pData[i]);
    if (i + 1 < DataSize) data |= ((uint32_t)pData[i + 1] << 8);
    if (i + 2 < DataSize) data |= ((uint32_t)pData[i + 2] << 16);
    if (i + 3 < DataSize) data |= ((uint32_t)pData[i + 3] << 24);

    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, address, data);
    if (status != HAL_OK)
    {
      HAL_FLASH_Lock();

      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }

    address += 4;
  }

  HAL_FLASH_Lock();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Read data from FLASH
HAL_StatusTypeDef FLASH_ReadData(uint8_t *pData, uint16_t DataSize)
{
  uint32_t address = FLASH_USER_START_ADDR;
  uint32_t data = 0;
  uint32_t i = 0;
  uint32_t primask;

  if (pData == NULL || DataSize > FLASH_PAGE_SIZE)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  for (i = 0; i < DataSize; i += 4)
  {
    data = *(__IO uint32_t *)address;

    if (i < DataSize) pData[i] = (uint8_t)(data & 0xFF);
    if (i + 1 < DataSize) pData[i + 1] = (uint8_t)((data >> 8) & 0xFF);
    if (i + 2 < DataSize) pData[i + 2] = (uint8_t)((data >> 16) & 0xFF);
    if (i + 3 < DataSize) pData[i + 3] = (uint8_t)((data >> 24) & 0xFF);

    address += 4;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

// Write single data item to FLASH
HAL_StatusTypeDef Flash_Write(uint8_t index, uint8_t dataType, uint32_t data)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t primask;

  if (index > FLASH_MAX_DATA_INDEX)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  flashDataBuffer[offset] = dataType;

  switch (dataType)
  {
    case FLASH_DATA_TYPE_UINT8:
      flashDataBuffer[offset + 1] = (uint8_t)(data & 0xFF);
      flashDataBuffer[offset + 2] = 0;
      flashDataBuffer[offset + 3] = 0;
      flashDataBuffer[offset + 4] = 0;
      flashDataBuffer[offset + 5] = 0;
      flashDataBuffer[offset + 6] = 0;
      flashDataBuffer[offset + 7] = 0;
      break;

    case FLASH_DATA_TYPE_UINT16:
      flashDataBuffer[offset + 1] = (uint8_t)(data & 0xFF);
      flashDataBuffer[offset + 2] = (uint8_t)((data >> 8) & 0xFF);
      flashDataBuffer[offset + 3] = 0;
      flashDataBuffer[offset + 4] = 0;
      flashDataBuffer[offset + 5] = 0;
      flashDataBuffer[offset + 6] = 0;
      flashDataBuffer[offset + 7] = 0;
      break;

    case FLASH_DATA_TYPE_UINT32:
    case FLASH_DATA_TYPE_FLOAT:
      flashDataBuffer[offset + 1] = (uint8_t)(data & 0xFF);
      flashDataBuffer[offset + 2] = (uint8_t)((data >> 8) & 0xFF);
      flashDataBuffer[offset + 3] = (uint8_t)((data >> 16) & 0xFF);
      flashDataBuffer[offset + 4] = (uint8_t)((data >> 24) & 0xFF);
      flashDataBuffer[offset + 5] = 0;
      flashDataBuffer[offset + 6] = 0;
      flashDataBuffer[offset + 7] = 0;
      break;

    case FLASH_DATA_TYPE_STRING:
      // For string type, data parameter is not used in this function
      // Use Flash_WriteString instead
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;

    default:
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;
  }

  status = Flash_SaveData();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Read single data item from FLASH
HAL_StatusTypeDef Flash_Read(uint8_t index, uint8_t dataType, void *pData)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t data = 0;
  uint32_t primask;

  if (index > FLASH_MAX_DATA_INDEX || pData == NULL)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  if (flashDataBuffer[offset] != dataType)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  switch (dataType)
  {
    case FLASH_DATA_TYPE_UINT8:
      *((uint8_t *)pData) = flashDataBuffer[offset + 1];
      break;

    case FLASH_DATA_TYPE_UINT16:
      data = (uint32_t)flashDataBuffer[offset + 1] | ((uint32_t)flashDataBuffer[offset + 2] << 8);
      *((uint16_t *)pData) = (uint16_t)data;
      break;

    case FLASH_DATA_TYPE_UINT32:
      data = (uint32_t)flashDataBuffer[offset + 1] |
             ((uint32_t)flashDataBuffer[offset + 2] << 8) |
             ((uint32_t)flashDataBuffer[offset + 3] << 16) |
             ((uint32_t)flashDataBuffer[offset + 4] << 24);
      *((uint32_t *)pData) = data;
      break;

    case FLASH_DATA_TYPE_FLOAT:
      data = (uint32_t)flashDataBuffer[offset + 1] |
             ((uint32_t)flashDataBuffer[offset + 2] << 8) |
             ((uint32_t)flashDataBuffer[offset + 3] << 16) |
             ((uint32_t)flashDataBuffer[offset + 4] << 24);
      *((float *)pData) = *((float *)&data);
      break;

    case FLASH_DATA_TYPE_STRING:
      // For string type, use Flash_ReadString instead
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;

    default:
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

// Write uint32_t data to FLASH
HAL_StatusTypeDef Flash_WriteUint32(uint8_t index, uint32_t value)
{
  return Flash_Write(index, FLASH_DATA_TYPE_UINT32, value);
}

// Read uint32_t data from FLASH
HAL_StatusTypeDef Flash_ReadUint32(uint8_t index, uint32_t *pValue)
{
  return Flash_Read(index, FLASH_DATA_TYPE_UINT32, pValue);
}

// Write string data to FLASH
HAL_StatusTypeDef Flash_WriteString(uint8_t index, const char *pString)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t primask;
  uint8_t stringLength;

  if (index > FLASH_MAX_DATA_INDEX || pString == NULL)
  {
    return HAL_ERROR;
  }

  stringLength = strlen(pString);
  if (stringLength > (FLASH_DATA_ITEM_SIZE - 2))  // Reserve 1 byte for type, 1 byte for length
  {
    return HAL_ERROR;  // String too long
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  // Set data type
  flashDataBuffer[offset] = FLASH_DATA_TYPE_STRING;

  // Set string length
  flashDataBuffer[offset + 1] = stringLength;

  // Copy string data (up to 6 bytes)
  for (uint8_t i = 0; i < stringLength && i < 6; i++)
  {
    flashDataBuffer[offset + 2 + i] = pString[i];
  }

  // Clear remaining bytes
  for (uint8_t i = stringLength; i < 6; i++)
  {
    flashDataBuffer[offset + 2 + i] = 0;
  }

  status = Flash_SaveData();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Read string data from FLASH
HAL_StatusTypeDef Flash_ReadString(uint8_t index, char *pString, uint8_t maxLength)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t primask;
  uint8_t stringLength;

  if (index > FLASH_MAX_DATA_INDEX || pString == NULL || maxLength == 0)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  if (flashDataBuffer[offset] != FLASH_DATA_TYPE_STRING)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  stringLength = flashDataBuffer[offset + 1];

  // Ensure we don't exceed buffer limits
  if (stringLength >= maxLength)
  {
    stringLength = maxLength - 1;
  }

  if (stringLength > 6)  // Maximum 6 bytes for string data
  {
    stringLength = 6;
  }

  // Copy string data
  for (uint8_t i = 0; i < stringLength; i++)
  {
    pString[i] = flashDataBuffer[offset + 2 + i];
  }

  // Null terminate
  pString[stringLength] = '\0';

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}
