/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    rtc_config.h
  * @brief   RTC时钟配置文件
  ******************************************************************************
  * @attention
  *
  * RTC时钟配置，使用LSE外部32.768KHz晶振时钟源
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#ifndef __RTC_CONFIG_H
#define __RTC_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* RTC时钟修正配置 */
#define RTC_CLOCK_CORRECTION_FACTOR 1.0f   // LSE时钟无需修正，精确32.768KHz
#define RTC_MAX_SLEEP_SECONDS_RAW 65535     // RTC硬件最大计数值

/* LSE时钟精确，无需修正，最大逻辑时间等于硬件最大值 */
#define RTC_MAX_LOGICAL_SLEEP_SECONDS 65535U

/* 函数声明 */
uint32_t RTC_ApplyClockCorrection(uint32_t logical_seconds, uint32_t *actual_logical_seconds);

#ifdef __cplusplus
}
#endif

#endif /* __RTC_CONFIG_H */
