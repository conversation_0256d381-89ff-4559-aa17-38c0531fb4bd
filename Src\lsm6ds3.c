#include "lsm6ds3.h"
#include <stdio.h>
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif
#include <math.h>
static void WriteReg(I2C_HandleTypeDef *hi2c, uint8_t reg, uint8_t val) {
    HAL_I2C_Mem_Write(hi2c, LSM6DS3_ADDR, reg, 1, &val, 1, HAL_MAX_DELAY);
}

void ReadRegs(I2C_HandleTypeDef *hi2c, uint8_t reg, uint8_t *buf, uint8_t len) {
    HAL_I2C_Mem_Read(hi2c, LSM6DS3_ADDR, reg, 1, buf, len, HAL_MAX_DELAY);
}

void LSM6DS3_Init(I2C_HandleTypeDef *hi2c) {
    WriteReg(hi2c, CTRL1_XL, 0x60);
    WriteReg(hi2c, CTRL2_G,  0x60);
    WriteReg(hi2c, CTRL3_C,  0x44);
    WriteReg(hi2c, CTRL4_C,  0x00);
    WriteReg(hi2c, CTRL5_C,  0x00);
    WriteReg(hi2c, CTRL6_C,  0x00);
    WriteReg(hi2c, CTRL7_G,  0x00);
    WriteReg(hi2c, CTRL8_XL, 0x00);
    WriteReg(hi2c, CTRL9_XL, 0x38);
    WriteReg(hi2c, CTRL10_C, 0x38);
    uint8_t dummy;
    ReadRegs(hi2c, WAKE_UP_SRC, &dummy, 1);
    ReadRegs(hi2c, TAP_SRC, &dummy, 1);
    ReadRegs(hi2c, D6D_SRC, &dummy, 1);
    ReadRegs(hi2c, STATUS_REG, &dummy, 1);
    printf("LSM6DS3 int OK\r\n");
}

void LSM6DS3_ReadData(I2C_HandleTypeDef *hi2c, LSM6DS3_Data *data) {
    uint8_t buf[12];
    uint8_t temp_buf[2];
    ReadRegs(hi2c, OUTX_L_G, buf, 12);
    int16_t gx = (int16_t)(buf[1] << 8 | buf[0]);
    int16_t gy = (int16_t)(buf[3] << 8 | buf[2]);
    int16_t gz = (int16_t)(buf[5] << 8 | buf[4]);
    int16_t ax = (int16_t)(buf[7] << 8 | buf[6]);
    int16_t ay = (int16_t)(buf[9] << 8 | buf[8]);
    int16_t az = (int16_t)(buf[11] << 8 | buf[10]);
    ReadRegs(hi2c, OUT_TEMP_L, temp_buf, 2);
    int16_t temp_raw = (int16_t)(temp_buf[1] << 8 | temp_buf[0]);
    data->gx = gx * 250.0f / 32768.0f;
    data->gy = gy * 250.0f / 32768.0f;
    data->gz = gz * 250.0f / 32768.0f;
    data->ax = ax * 2.0f / 32768.0f;
    data->ay = ay * 2.0f / 32768.0f;
    data->az = az * 2.0f / 32768.0f;
    data->temp_celsius = 25.0f + (float)temp_raw / 256.0f;
}

float LSM6DS3_GetPitch(const LSM6DS3_Data *data) {
    return atan2f(data->ax, sqrtf(data->ay * data->ay + data->az * data->az)) * 180.0f / M_PI;
}

float LSM6DS3_GetRoll(const LSM6DS3_Data *data) {
    return atan2f(data->ay, sqrtf(data->ax * data->ax + data->az * data->az)) * 180.0f / M_PI;
}

/**
 * @brief 初始化姿态角结构体
 * @param attitude 姿态角结构体指针
 */
void LSM6DS3_InitAttitude(LSM6DS3_Attitude *attitude) {
    attitude->pitch = 0.0f;
    attitude->roll = 0.0f;
    attitude->yaw = 0.0f;
    attitude->lastPitch = 0.0f;
    attitude->lastRoll = 0.0f;
    attitude->lastYaw = 0.0f;
    attitude->lastUpdateTime = HAL_GetTick();
    attitude->initialized = 0;
}

/**
 * @brief 互补滤波算法实现
 * @param data 传感器数据
 * @param attitude 姿态角结构体指针
 */
void LSM6DS3_ComplementaryFilter(LSM6DS3_Data *data, LSM6DS3_Attitude *attitude) {
    uint32_t now = HAL_GetTick();
    float dt;
    float accel_pitch = LSM6DS3_GetPitch(data);
    float accel_roll = LSM6DS3_GetRoll(data);
    if (!attitude->initialized) {
        attitude->pitch = accel_pitch;
        attitude->roll = accel_roll;
        attitude->yaw = 0.0f;
        attitude->lastPitch = accel_pitch;
        attitude->lastRoll = accel_roll;
        attitude->lastYaw = 0.0f;
        attitude->initialized = 1;
        attitude->lastUpdateTime = now;
        return;
    }

    dt = (float)(now - attitude->lastUpdateTime) / 1000.0f;
    attitude->lastUpdateTime = now;
    if (dt <= 0.0f || dt > 1.0f) {
        dt = SAMPLE_TIME;
    }

    float gyro_pitch = attitude->pitch + data->gx * dt;
    float gyro_roll = attitude->roll + data->gy * dt;
    float gyro_yaw = attitude->yaw + data->gz * dt;
    attitude->pitch = COMP_FILTER_ALPHA * gyro_pitch + (1.0f - COMP_FILTER_ALPHA) * accel_pitch;
    attitude->roll = COMP_FILTER_ALPHA * gyro_roll + (1.0f - COMP_FILTER_ALPHA) * accel_roll;
		 attitude->yaw += data->gz * dt;
    attitude->lastPitch = attitude->pitch;
    attitude->lastRoll = attitude->roll;
    attitude->lastYaw = attitude->yaw;
    while (attitude->yaw > 360.0f) attitude->yaw -= 360.0f;
    while (attitude->yaw < 0.0f) attitude->yaw += 360.0f;
}

